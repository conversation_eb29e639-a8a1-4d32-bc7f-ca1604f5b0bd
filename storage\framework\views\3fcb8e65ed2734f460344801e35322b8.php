<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title><?php echo $__env->yieldContent('title'); ?></title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('hope-ui/assets/images/favicon.ico')); ?>" />

    <!-- Library / Plugin Css Build -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/css/core/libs.min.css')); ?>" />

    <!-- Flaticon Css -->
    

    <!-- Aos Animation Css -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/vendor/aos/dist/aos.css')); ?>" />

    <!-- Hope Ui Design System Css -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/css/hope-ui.min.css?v=2.0.0')); ?>" />

    <!-- Custom Css -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/css/custom.min.css?v=2.0.0')); ?>" />

    <!-- Dark Css -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/css/dark.min.css')); ?>" />

    <!-- Customizer Css -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/css/customizer.min.css')); ?>" />

    <!-- RTL Css -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/css/rtl.min.css')); ?>" />

    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/vendor/flatpickr/dist/flatpickr.min.css')); ?>" />

    <!-- sweetalert Css -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/assets/css/sweetalert.min.css')); ?>" />
    <?php echo $__env->yieldContent('meta'); ?>
    <style>
        .activeSidebar {
            background: var(--bs-primary-tint-90);
            color: var(--bs-primary) !important;
        }

        .pagination {
            margin-bottom: 0;
        }

        .Table-custom-padding1 tfoot {
            border: 1px solid #fff;
        }

        .Table-custom-padding1 tfoot td {
            padding-top: 1em;
        }
    </style>
    <?php echo $__env->yieldContent('style'); ?>
</head>

<body class="  ">
    <div id="logout-loader">

    </div>
    <!-- loader Start -->
    <div id="loading">
        <div class="loader simple-loader">
            <div class="loader-body"></div>
        </div>
    </div>
    <!-- loader END -->

    <?php echo $__env->make('admin.layouts.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <main class="main-content d-flex flex-column">
        <?php echo $__env->make('admin.layouts.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <div class="container-fluid content-inner p-3">
            <div class="row">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
        <!-- Footer Section Start -->
        <?php echo $__env->make('admin.layouts.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!-- Footer Section End -->
    </main>
    <?php echo $__env->yieldContent('modal'); ?>
    <!-- Library Bundle Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/core/libs.min.js')); ?>"></script>

    <!-- External Library Bundle Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/core/external.min.js')); ?>"></script>

    <!-- Widgetchart Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/charts/widgetcharts.js')); ?>"></script>

    <!-- mapchart Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/charts/vectore-chart.js')); ?>"></script>
    <script src="<?php echo e(asset('hope-ui/assets/js/charts/dashboard.js')); ?>"></script>

    <!-- fslightbox Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/plugins/fslightbox.js')); ?>"></script>

    <!-- Settings Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/plugins/setting.js')); ?>"></script>

    <!-- Slider-tab Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/plugins/slider-tabs.js')); ?>"></script>

    <!-- Form Wizard Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/plugins/form-wizard.js')); ?>"></script>

    <!-- AOS Animation Plugin-->
    <script src="<?php echo e(asset('hope-ui/assets/vendor/aos/dist/aos.js')); ?>"></script>

    <!-- App Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/hope-ui.js')); ?>" defer></script>

    <!-- Patient portal js start -->

    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <!--  Flatpickr  -->


    <!-- apexchart external js for delete  -->

    <!-- External Library Bundle Script -->
    <script src="<?php echo e(asset('hope-ui/assets/js/core/external.min.js')); ?>"></script>



    <!-- apexchart external js for delete  -->

    <script src="<?php echo e(asset('hope-ui/assets/js/charts/utility.min.js')); ?>"></script>
    <script src="<?php echo e(asset('hope-ui/assets/js/custom/health_summary_apexcharts.js')); ?>"></script>






    <!--Select2 js-->
    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"> -->
    <link rel="stylesheet" href="<?php echo e(asset('hope-ui/dev_assets/select2/custom/select2.min.css')); ?>" />

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- sweetalert Js -->
    <script rel='text/javascript' src="<?php echo e(asset('hope-ui/assets//js/sweetalert.min.js')); ?>"></script>
    <!-- Flatpickr css -->
    <script src="<?php echo e(asset('hope-ui/assets/vendor/flatpickr/dist/flatpickr.min.js')); ?>"></script>
    
    <?php echo $__env->yieldPushContent('scripts-head'); ?>
    <script src="<?php echo e(asset('hope-ui/assets/js/custom.js')); ?>"></script>
    <script>
        $(document).ready(function() {
            // Disable back button for this page
            function initBackButtonDisable() {
                // Push current state to history
                if (window.history && window.history.pushState) {
                    window.history.pushState('forward', null, window.location.href);

                    $(window).on('popstate', function() {
                        window.history.pushState('forward', null, window.location.href);

                        // Optional: Show notification
                        <?php if(config('app.debug')): ?>
                            console.log('Back navigation disabled');
                        <?php endif; ?>
                    });
                }
            }

            // Initialize
            initBackButtonDisable();

            // Optional: Add visual indicator
            $('body').append('<div id="nav-status" style="display:none;">Navigation disabled</div>');
        });
    </script>
    <script>
        function validatePhoneNumber(phone) {
            const errors = {};

            if (!phone) {
                errors.phone = 'The phone number field is required.';
            } else if (!/^[0-9]{10}$/.test(phone)) {
                errors.phone = 'The phone number must be 10 digits.';
            }

            return errors;
        }

        function validateRewardPoint(redeem_points) {
            if (!Number.isInteger(redeem_points)) {
                const response = {
                    success: false,
                    message: `Reward points must be rounded to the nearest whole number.`
                };
                return response; // assuming you're using Express.js
            } else {
                return null;
            }
        }
    </script>

    <script>
        var refreashUrlActual = "<?php echo e(config('authorization.auth_url') . 'refresh'); ?>";
        var loginUrlActual = "<?php echo e(route('login')); ?>";
        setLoginUrl(loginUrlActual);
        setRefreashUrl(refreashUrlActual);

        $(document).ready(function() {
            // customRefresh(refreashUrl, loginUrl);
            let logoutLoadingGif = '<?php echo e(asset('hope-ui/assets/images/loader/custom-loader.gif')); ?>';
            logoutLoading(logoutLoadingGif);
            // Remove data
            // console.log(localStorage.getItem('device_check'));
            localStorage.removeItem('device_check');
        });
    </script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
    <script>
        $(function test() {
            $('.select2-multpl-custom').select2({
                dropdownParent: $('.selct-modal')
            });
        });
    </script>

    <script>
        $(function test() {
            $('.select2-multpl-custom1').select2();
            // $('.select2-multpl-custom1').select2({
            //     dropdownParent: $('.selct-modal1')
            // });
        });
    </script>

    <script>
        $(function test() {
            $('.select2-multpl-custom2').select2({
                dropdownParent: $('.selct-modal2')
            });
        });
        $(function test() {
            $('.select2-multpl-custom4').select2({
                dropdownParent: $('.selct-modal4')
            });
        });
    </script>



    <!-- Patient portal js end-->





</body>

</html>
<?php /**PATH C:\wamp64\www\mymd-care\resources\views/admin/layouts/app.blade.php ENDPATH**/ ?>