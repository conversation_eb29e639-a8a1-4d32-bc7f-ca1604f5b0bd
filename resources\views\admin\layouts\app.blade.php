<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>@yield('title')</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('hope-ui/assets/images/favicon.ico') }}" />

    <!-- Library / Plugin Css Build -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/core/libs.min.css') }}" />

    <!-- Flaticon Css -->
    {{-- <link rel="stylesheet" href="{{ asset('hope-ui/assets/flaticon/font/flaticon.css') }}"/> --}}

    <!-- Aos Animation Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/vendor/aos/dist/aos.css') }}" />

    <!-- Hope Ui Design System Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/hope-ui.min.css?v=2.0.0') }}" />

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/custom.min.css?v=2.0.0') }}" />

    <!-- Dark Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/dark.min.css') }}" />

    <!-- Customizer Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/customizer.min.css') }}" />

    <!-- RTL Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/rtl.min.css') }}" />

    <link rel="stylesheet" href="{{ asset('hope-ui/assets/vendor/flatpickr/dist/flatpickr.min.css') }}" />

    <!-- sweetalert Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/sweetalert.min.css') }}" />
    @yield('meta')
    <style>
        .activeSidebar {
            background: var(--bs-primary-tint-90);
            color: var(--bs-primary) !important;
        }

        .pagination {
            margin-bottom: 0;
        }

        .Table-custom-padding1 tfoot {
            border: 1px solid #fff;
        }

        .Table-custom-padding1 tfoot td {
            padding-top: 1em;
        }
    </style>
    @yield('style')
</head>

<body class="  ">
    <div id="logout-loader">

    </div>
    <!-- loader Start -->
    <div id="loading">
        <div class="loader simple-loader">
            <div class="loader-body"></div>
        </div>
    </div>
    <!-- loader END -->

    @include('admin.layouts.sidebar')
    <main class="main-content d-flex flex-column">
        @include('admin.layouts.header')
        <div class="container-fluid content-inner p-3">
            <div class="row">
                @yield('content')
            </div>
        </div>
        <!-- Footer Section Start -->
        @include('admin.layouts.footer')
        <!-- Footer Section End -->
    </main>
    @yield('modal')
    <!-- Library Bundle Script -->
    <script src="{{ asset('hope-ui/assets/js/core/libs.min.js') }}"></script>

    <!-- External Library Bundle Script -->
    <script src="{{ asset('hope-ui/assets/js/core/external.min.js') }}"></script>

    <!-- Widgetchart Script -->
    <script src="{{ asset('hope-ui/assets/js/charts/widgetcharts.js') }}"></script>

    <!-- mapchart Script -->
    <script src="{{ asset('hope-ui/assets/js/charts/vectore-chart.js') }}"></script>
    <script src="{{ asset('hope-ui/assets/js/charts/dashboard.js') }}"></script>

    <!-- fslightbox Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/fslightbox.js') }}"></script>

    <!-- Settings Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/setting.js') }}"></script>

    <!-- Slider-tab Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/slider-tabs.js') }}"></script>

    <!-- Form Wizard Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/form-wizard.js') }}"></script>

    <!-- AOS Animation Plugin-->
    <script src="{{ asset('hope-ui/assets/vendor/aos/dist/aos.js') }}"></script>

    <!-- App Script -->
    <script src="{{ asset('hope-ui/assets/js/hope-ui.js') }}" defer></script>

    <!-- Patient portal js start -->

    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <!--  Flatpickr  -->


    <!-- apexchart external js for delete  -->

    <!-- External Library Bundle Script -->
    <script src="{{ asset('hope-ui/assets/js/core/external.min.js') }}"></script>



    <!-- apexchart external js for delete  -->

    <script src="{{ asset('hope-ui/assets/js/charts/utility.min.js') }}"></script>
    <script src="{{ asset('hope-ui/assets/js/custom/health_summary_apexcharts.js') }}"></script>






    <!--Select2 js-->
    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"> -->
    <link rel="stylesheet" href="{{ asset('hope-ui/dev_assets/select2/custom/select2.min.css') }}" />

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- sweetalert Js -->
    <script rel='text/javascript' src="{{ asset('hope-ui/assets//js/sweetalert.min.js') }}"></script>
    <!-- Flatpickr css -->
    <script src="{{ asset('hope-ui/assets/vendor/flatpickr/dist/flatpickr.min.js') }}"></script>
    {{-- <script>
        $("#date_range").flatpickr({
                mode: "range",
                //   maxDate: "today"
            });
    </script> --}}
    @stack('scripts-head')
    <script src="{{ asset('hope-ui/assets/js/custom.js') }}"></script>
    <script>
        // // Prevent back button by manipulating browser history
        // (function() {
        //     // Add a dummy state to history
        //     history.pushState(null, null, location.href);
        //     console.log(history);

        //     // Listen for popstate event (triggered by back/forward buttons)
        //     window.addEventListener('popstate', function(event) {
        //         // Push the current state again to prevent going back
        //         history.pushState(null, null, location.href);
        //     });
        // })();
        // Prevent back button by manipulating browser history
        (function() {
            // Add a dummy state to history
            history.pushState(null, null, location.href);

            // Listen for popstate event (triggered by back/forward buttons)
            window.addEventListener('popstate', function(event) {
                // Push the current state again to prevent going back
                history.pushState(null, null, location.href);
            });
        })();

        function validatePhoneNumber(phone) {
            const errors = {};

            if (!phone) {
                errors.phone = 'The phone number field is required.';
            } else if (!/^[0-9]{10}$/.test(phone)) {
                errors.phone = 'The phone number must be 10 digits.';
            }

            return errors;
        }

        function validateRewardPoint(redeem_points) {
            if (!Number.isInteger(redeem_points)) {
                const response = {
                    success: false,
                    message: `Reward points must be rounded to the nearest whole number.`
                };
                return response; // assuming you're using Express.js
            } else {
                return null;
            }
        }
    </script>

    <script>
        var refreashUrlActual = "{{ config('authorization.auth_url') . 'refresh' }}";
        var loginUrlActual = "{{ route('login') }}";
        setLoginUrl(loginUrlActual);
        setRefreashUrl(refreashUrlActual);

        $(document).ready(function() {
            // customRefresh(refreashUrl, loginUrl);
            let logoutLoadingGif = '{{ asset('hope-ui/assets/images/loader/custom-loader.gif') }}';
            logoutLoading(logoutLoadingGif);
            // Remove data
            // console.log(localStorage.getItem('device_check'));
            localStorage.removeItem('device_check');
        });
    </script>
    @stack('scripts')
    <script>
        $(function test() {
            $('.select2-multpl-custom').select2({
                dropdownParent: $('.selct-modal')
            });
        });
    </script>

    <script>
        $(function test() {
            $('.select2-multpl-custom1').select2();
            // $('.select2-multpl-custom1').select2({
            //     dropdownParent: $('.selct-modal1')
            // });
        });
    </script>

    <script>
        $(function test() {
            $('.select2-multpl-custom2').select2({
                dropdownParent: $('.selct-modal2')
            });
        });
        $(function test() {
            $('.select2-multpl-custom4').select2({
                dropdownParent: $('.selct-modal4')
            });
        });
    </script>



    <!-- Patient portal js end-->





</body>

</html>
